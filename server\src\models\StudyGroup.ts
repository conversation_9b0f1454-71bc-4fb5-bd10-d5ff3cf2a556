import mongoose, { Schema, Document } from 'mongoose';
import { StudyGroup as IStudyGroup } from '../../../shared/types';

export interface StudyGroupDocument extends Omit<IStudyGroup, '_id'>, Document {}

const StudyGroupSchema = new Schema({
  name: {
    type: String,
    required: [true, 'Study group name is required'],
    trim: true,
    minlength: [3, 'Name must be at least 3 characters long'],
    maxlength: [100, 'Name cannot exceed 100 characters']
  },
  description: {
    type: String,
    required: [true, 'Description is required'],
    trim: true,
    maxlength: [500, 'Description cannot exceed 500 characters']
  },
  creator: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'Creator is required']
  },
  members: [{
    type: Schema.Types.ObjectId,
    ref: 'User'
  }],
  moderators: [{
    type: Schema.Types.ObjectId,
    ref: 'User'
  }],
  quizzes: [{
    type: Schema.Types.ObjectId,
    ref: 'Quiz'
  }],
  isPublic: {
    type: Boolean,
    default: true
  },
  maxMembers: {
    type: Number,
    min: [2, 'Max members must be at least 2'],
    max: [1000, 'Max members cannot exceed 1000']
  }
}, {
  timestamps: true,
  toJSON: {
    transform: function(doc, ret) {
      delete ret.__v;
      return ret;
    }
  }
});

// Indexes
StudyGroupSchema.index({ creator: 1 });
StudyGroupSchema.index({ members: 1 });
StudyGroupSchema.index({ isPublic: 1 });
StudyGroupSchema.index({ name: 'text', description: 'text' });

// Virtual for member count
StudyGroupSchema.virtual('memberCount').get(function() {
  return this.members.length;
});

// Pre-save middleware
StudyGroupSchema.pre('save', function(next) {
  // Add creator to members if not already included
  if (!this.members.includes(this.creator)) {
    this.members.push(this.creator);
  }
  
  // Add creator to moderators if not already included
  if (!this.moderators.includes(this.creator)) {
    this.moderators.push(this.creator);
  }
  
  next();
});

// Method to add member
StudyGroupSchema.methods.addMember = function(userId: string) {
  if (this.maxMembers && this.members.length >= this.maxMembers) {
    throw new Error('Study group is full');
  }
  
  if (!this.members.includes(userId)) {
    this.members.push(userId);
  }
};

// Method to remove member
StudyGroupSchema.methods.removeMember = function(userId: string) {
  this.members = this.members.filter((id: string) => id.toString() !== userId);
  this.moderators = this.moderators.filter((id: string) => id.toString() !== userId);
};

// Method to add moderator
StudyGroupSchema.methods.addModerator = function(userId: string) {
  if (!this.members.includes(userId)) {
    throw new Error('User must be a member to become a moderator');
  }
  
  if (!this.moderators.includes(userId)) {
    this.moderators.push(userId);
  }
};

// Method to check if user is member
StudyGroupSchema.methods.isMember = function(userId: string): boolean {
  return this.members.some((id: string) => id.toString() === userId);
};

// Method to check if user is moderator
StudyGroupSchema.methods.isModerator = function(userId: string): boolean {
  return this.moderators.some((id: string) => id.toString() === userId);
};

export const StudyGroup = mongoose.model<StudyGroupDocument>('StudyGroup', StudyGroupSchema);
