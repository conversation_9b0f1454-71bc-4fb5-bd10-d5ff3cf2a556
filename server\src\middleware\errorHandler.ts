import { Request, Response, NextFunction } from 'express';
import { logger } from '../config/logger';
import { ValidationError } from 'express-validator';

export interface AppError extends Error {
  statusCode?: number;
  isOperational?: boolean;
}

/**
 * Create operational error
 */
export const createError = (message: string, statusCode: number = 500): AppError => {
  const error: AppError = new Error(message);
  error.statusCode = statusCode;
  error.isOperational = true;
  return error;
};

/**
 * Handle MongoDB cast errors
 */
const handleCastError = (error: any): AppError => {
  const message = `Invalid ${error.path}: ${error.value}`;
  return createError(message, 400);
};

/**
 * Handle MongoDB duplicate key errors
 */
const handleDuplicateKeyError = (error: any): AppError => {
  const field = Object.keys(error.keyValue)[0];
  const value = error.keyValue[field];
  const message = `${field.charAt(0).toUpperCase() + field.slice(1)} '${value}' already exists`;
  return createError(message, 409);
};

/**
 * Handle MongoDB validation errors
 */
const handleValidationError = (error: any): AppError => {
  const errors = Object.values(error.errors).map((err: any) => err.message);
  const message = `Validation failed: ${errors.join('. ')}`;
  return createError(message, 400);
};

/**
 * Handle JWT errors
 */
const handleJWTError = (): AppError => {
  return createError('Invalid token. Please log in again', 401);
};

/**
 * Handle JWT expired errors
 */
const handleJWTExpiredError = (): AppError => {
  return createError('Token expired. Please log in again', 401);
};

/**
 * Send error response in development
 */
const sendErrorDev = (error: AppError, res: Response) => {
  res.status(error.statusCode || 500).json({
    success: false,
    error: error.message,
    stack: error.stack,
    details: error
  });
};

/**
 * Send error response in production
 */
const sendErrorProd = (error: AppError, res: Response) => {
  // Operational, trusted error: send message to client
  if (error.isOperational) {
    res.status(error.statusCode || 500).json({
      success: false,
      error: error.message
    });
  } else {
    // Programming or other unknown error: don't leak error details
    logger.error('ERROR:', error);
    
    res.status(500).json({
      success: false,
      error: 'Something went wrong'
    });
  }
};

/**
 * Global error handling middleware
 */
export const errorHandler = (
  error: AppError,
  req: Request,
  res: Response,
  next: NextFunction
) => {
  let err = { ...error };
  err.message = error.message;

  // Log error
  logger.error(`Error ${error.statusCode || 500}: ${error.message}`, {
    url: req.url,
    method: req.method,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    stack: error.stack
  });

  // MongoDB cast error
  if (error.name === 'CastError') {
    err = handleCastError(error);
  }

  // MongoDB duplicate key error
  if (error.code === 11000) {
    err = handleDuplicateKeyError(error);
  }

  // MongoDB validation error
  if (error.name === 'ValidationError') {
    err = handleValidationError(error);
  }

  // JWT error
  if (error.name === 'JsonWebTokenError') {
    err = handleJWTError();
  }

  // JWT expired error
  if (error.name === 'TokenExpiredError') {
    err = handleJWTExpiredError();
  }

  // Send error response
  if (process.env.NODE_ENV === 'development') {
    sendErrorDev(err, res);
  } else {
    sendErrorProd(err, res);
  }
};

/**
 * Async error wrapper
 */
export const asyncHandler = (fn: Function) => {
  return (req: Request, res: Response, next: NextFunction) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};

/**
 * Not found middleware
 */
export const notFound = (req: Request, res: Response, next: NextFunction) => {
  const error = createError(`Not found - ${req.originalUrl}`, 404);
  next(error);
};
