{"name": "quizcraft-server", "version": "1.0.0", "description": "QuizCraft Backend API Server", "main": "dist/index.js", "scripts": {"dev": "nodemon src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "format": "prettier --write src/**/*.ts", "db:seed": "ts-node src/scripts/seed.ts", "db:migrate": "ts-node src/scripts/migrate.ts"}, "dependencies": {"express": "^4.18.2", "mongoose": "^8.0.3", "redis": "^4.6.11", "socket.io": "^4.7.4", "jsonwebtoken": "^9.0.2", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "helmet": "^7.1.0", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "multer": "^1.4.5-lts.1", "sharp": "^0.33.1", "tesseract.js": "^5.0.4", "openai": "^4.20.1", "nodemailer": "^6.9.7", "twilio": "^4.19.0", "winston": "^3.11.0", "compression": "^1.7.4", "express-mongo-sanitize": "^2.2.0", "xss": "^1.0.14", "hpp": "^0.2.3", "dotenv": "^16.3.1", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.0", "pdf-parse": "^1.1.1", "node-cron": "^3.0.3"}, "devDependencies": {"@types/express": "^4.17.21", "@types/node": "^20.10.4", "@types/cors": "^2.8.17", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.5", "@types/multer": "^1.4.11", "@types/nodemailer": "^6.4.14", "@types/compression": "^1.7.5", "@types/hpp": "^0.2.5", "@types/swagger-jsdoc": "^6.0.4", "@types/swagger-ui-express": "^4.1.6", "@types/pdf-parse": "^1.1.4", "@types/node-cron": "^3.0.11", "@types/jest": "^29.5.8", "@types/supertest": "^2.0.16", "typescript": "^5.3.3", "ts-node": "^10.9.1", "nodemon": "^3.0.2", "jest": "^29.7.0", "ts-jest": "^29.1.1", "supertest": "^6.3.3", "eslint": "^8.55.0", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1", "prettier": "^3.1.0"}, "jest": {"preset": "ts-jest", "testEnvironment": "node", "roots": ["<rootDir>/src"], "testMatch": ["**/__tests__/**/*.ts", "**/?(*.)+(spec|test).ts"], "collectCoverageFrom": ["src/**/*.ts", "!src/**/*.d.ts", "!src/index.ts"]}}