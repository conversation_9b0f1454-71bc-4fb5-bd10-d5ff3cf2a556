{"name": "quizcraft", "version": "1.0.0", "description": "AI-Powered Learning & Quiz Platform", "main": "index.js", "scripts": {"dev": "concurrently \"npm run dev:server\" \"npm run dev:client\"", "dev:server": "cd server && npm run dev", "dev:client": "cd client && npm run dev", "build": "npm run build:client && npm run build:server", "build:client": "cd client && npm run build", "build:server": "cd server && npm run build", "start": "cd server && npm start", "test": "npm run test:server && npm run test:client", "test:server": "cd server && npm test", "test:client": "cd client && npm test", "test:e2e": "cypress run", "lint": "npm run lint:server && npm run lint:client", "lint:server": "cd server && npm run lint", "lint:client": "cd client && npm run lint", "format": "prettier --write \"**/*.{js,jsx,ts,tsx,json,md}\"", "prepare": "husky install", "docker:build": "docker-compose build", "docker:up": "docker-compose up", "docker:down": "docker-compose down"}, "keywords": ["quiz", "learning", "ai", "education", "react", "nodejs", "typescript", "mongodb"], "author": "QuizCraft Team", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2", "cypress": "^13.6.0", "husky": "^8.0.3", "lint-staged": "^15.2.0", "prettier": "^3.1.0"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "*.{json,md}": ["prettier --write"]}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "https://github.com/yourusername/quizcraft.git"}, "bugs": {"url": "https://github.com/yourusername/quizcraft/issues"}, "homepage": "https://github.com/yourusername/quizcraft#readme"}