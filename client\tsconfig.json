{"compilerOptions": {"target": "ES2020", "useDefineForClassFields": true, "lib": ["ES2020", "DOM", "DOM.Iterable"], "module": "ESNext", "skipLibCheck": true, "moduleResolution": "bundler", "allowImportingTsExtensions": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "strict": true, "noUnusedLocals": true, "noUnusedParameters": true, "noFallthroughCasesInSwitch": true, "baseUrl": "./src", "paths": {"@/*": ["./*"], "@/components/*": ["./components/*"], "@/pages/*": ["./pages/*"], "@/hooks/*": ["./hooks/*"], "@/services/*": ["./services/*"], "@/utils/*": ["./utils/*"], "@/types/*": ["./types/*"], "@/styles/*": ["./styles/*"]}}, "include": ["src", "vite.config.ts"], "references": [{"path": "./tsconfig.node.json"}]}