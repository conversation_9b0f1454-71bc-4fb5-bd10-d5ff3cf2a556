import mongoose from 'mongoose';
import { logger } from './logger';

const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/quizcraft';

export const connectDatabase = async (): Promise<void> => {
  try {
    const options: mongoose.ConnectOptions = {
      maxPoolSize: 10, // Maintain up to 10 socket connections
      serverSelectionTimeoutMS: 5000, // Keep trying to send operations for 5 seconds
      socketTimeoutMS: 45000, // Close sockets after 45 seconds of inactivity
      bufferMaxEntries: 0, // Disable mongoose buffering
      bufferCommands: false, // Disable mongoose buffering
    };

    await mongoose.connect(MONGODB_URI, options);
    
    logger.info('✅ Connected to MongoDB successfully');
    
    // Handle connection events
    mongoose.connection.on('error', (error) => {
      logger.error('❌ MongoDB connection error:', error);
    });

    mongoose.connection.on('disconnected', () => {
      logger.warn('⚠️ MongoDB disconnected');
    });

    mongoose.connection.on('reconnected', () => {
      logger.info('🔄 MongoDB reconnected');
    });

    // Graceful shutdown
    process.on('SIGINT', async () => {
      await mongoose.connection.close();
      logger.info('🛑 MongoDB connection closed through app termination');
      process.exit(0);
    });

  } catch (error) {
    logger.error('❌ Failed to connect to MongoDB:', error);
    process.exit(1);
  }
};

export const disconnectDatabase = async (): Promise<void> => {
  try {
    await mongoose.connection.close();
    logger.info('🛑 MongoDB connection closed');
  } catch (error) {
    logger.error('❌ Error closing MongoDB connection:', error);
  }
};

// Database health check
export const checkDatabaseHealth = async (): Promise<boolean> => {
  try {
    const state = mongoose.connection.readyState;
    return state === 1; // 1 = connected
  } catch (error) {
    logger.error('❌ Database health check failed:', error);
    return false;
  }
};

// Create indexes for better performance
export const createIndexes = async (): Promise<void> => {
  try {
    const db = mongoose.connection.db;
    
    // User indexes
    await db.collection('users').createIndex({ email: 1 }, { unique: true });
    await db.collection('users').createIndex({ username: 1 }, { unique: true });
    await db.collection('users').createIndex({ 'stats.ranking': -1 });
    await db.collection('users').createIndex({ createdAt: -1 });
    
    // Quiz indexes
    await db.collection('quizzes').createIndex({ creator: 1 });
    await db.collection('quizzes').createIndex({ category: 1 });
    await db.collection('quizzes').createIndex({ tags: 1 });
    await db.collection('quizzes').createIndex({ difficulty: 1 });
    await db.collection('quizzes').createIndex({ isPublic: 1, isActive: 1 });
    await db.collection('quizzes').createIndex({ 'stats.popularity': -1 });
    await db.collection('quizzes').createIndex({ createdAt: -1 });
    
    // Quiz attempt indexes
    await db.collection('quizattempts').createIndex({ userId: 1, quizId: 1 });
    await db.collection('quizattempts').createIndex({ quizId: 1, completedAt: -1 });
    await db.collection('quizattempts').createIndex({ userId: 1, completedAt: -1 });
    
    // Study group indexes
    await db.collection('studygroups').createIndex({ creator: 1 });
    await db.collection('studygroups').createIndex({ members: 1 });
    await db.collection('studygroups').createIndex({ isPublic: 1 });
    
    // Analytics indexes
    await db.collection('analytics').createIndex({ userId: 1, timestamp: -1 });
    await db.collection('analytics').createIndex({ type: 1, timestamp: -1 });
    
    // Multiplayer session indexes
    await db.collection('multiplayersessions').createIndex({ hostId: 1 });
    await db.collection('multiplayersessions').createIndex({ 'participants.userId': 1 });
    await db.collection('multiplayersessions').createIndex({ status: 1, createdAt: -1 });
    
    // Notification indexes
    await db.collection('notifications').createIndex({ userId: 1, createdAt: -1 });
    await db.collection('notifications').createIndex({ userId: 1, isRead: 1 });
    
    // Learning path indexes
    await db.collection('learningpaths').createIndex({ userId: 1, subject: 1 });
    
    logger.info('✅ Database indexes created successfully');
  } catch (error) {
    logger.error('❌ Failed to create database indexes:', error);
  }
};
