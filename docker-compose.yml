version: '3.8'

services:
  # MongoDB Database
  mongodb:
    image: mongo:7.0
    container_name: quizcraft-mongodb
    restart: unless-stopped
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONG<PERSON>_INITDB_ROOT_PASSWORD: password123
      MONGO_INITDB_DATABASE: quizcraft
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db
      - ./scripts/mongo-init.js:/docker-entrypoint-initdb.d/mongo-init.js:ro
    networks:
      - quizcraft-network

  # Redis Cache
  redis:
    image: redis:7.2-alpine
    container_name: quizcraft-redis
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass redis123
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - quizcraft-network

  # Backend API Server
  server:
    build:
      context: ./server
      dockerfile: Dockerfile
    container_name: quizcraft-server
    restart: unless-stopped
    environment:
      NODE_ENV: production
      PORT: 5000
      MONGODB_URI: ********************************************************************
      REDIS_URL: redis://:redis123@redis:6379
      JWT_SECRET: your-production-jwt-secret
      JWT_REFRESH_SECRET: your-production-refresh-secret
      OPENAI_API_KEY: ${OPENAI_API_KEY}
      SENDGRID_API_KEY: ${SENDGRID_API_KEY}
      TWILIO_ACCOUNT_SID: ${TWILIO_ACCOUNT_SID}
      TWILIO_AUTH_TOKEN: ${TWILIO_AUTH_TOKEN}
    ports:
      - "5000:5000"
    depends_on:
      - mongodb
      - redis
    volumes:
      - ./server/uploads:/app/uploads
      - ./server/logs:/app/logs
    networks:
      - quizcraft-network

  # Frontend React App
  client:
    build:
      context: ./client
      dockerfile: Dockerfile
    container_name: quizcraft-client
    restart: unless-stopped
    environment:
      VITE_API_URL: http://localhost:5000/api
      VITE_SOCKET_URL: http://localhost:5000
    ports:
      - "3000:80"
    depends_on:
      - server
    networks:
      - quizcraft-network

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: quizcraft-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
    depends_on:
      - client
      - server
    networks:
      - quizcraft-network

volumes:
  mongodb_data:
    driver: local
  redis_data:
    driver: local

networks:
  quizcraft-network:
    driver: bridge
