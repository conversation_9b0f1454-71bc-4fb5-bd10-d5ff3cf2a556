import mongoose, { Schema, Document } from 'mongoose';
import bcrypt from 'bcryptjs';
import { User as IUser, UserRole, UserPreferences, UserStats, Achievement } from '../../../shared/types';

export interface UserDocument extends Omit<IUser, '_id'>, Document {
  password: string;
  refreshTokens: string[];
  emailVerificationToken?: string;
  passwordResetToken?: string;
  passwordResetExpires?: Date;
  comparePassword(candidatePassword: string): Promise<boolean>;
  generatePasswordResetToken(): string;
}

const NotificationSettingsSchema = new Schema({
  email: { type: Boolean, default: true },
  push: { type: Boolean, default: true },
  sms: { type: Boolean, default: false },
  quizReminders: { type: Boolean, default: true },
  achievementAlerts: { type: Boolean, default: true },
  studyGroupUpdates: { type: Boolean, default: true }
}, { _id: false });

const AccessibilitySettingsSchema = new Schema({
  fontSize: { type: String, enum: ['small', 'medium', 'large'], default: 'medium' },
  highContrast: { type: Boolean, default: false },
  screenReader: { type: Boolean, default: false },
  dyslexiaFriendly: { type: Boolean, default: false },
  reducedMotion: { type: Boolean, default: false }
}, { _id: false });

const PrivacySettingsSchema = new Schema({
  profileVisibility: { type: String, enum: ['public', 'friends', 'private'], default: 'public' },
  showStats: { type: Boolean, default: true },
  showAchievements: { type: Boolean, default: true },
  allowFriendRequests: { type: Boolean, default: true }
}, { _id: false });

const UserPreferencesSchema = new Schema({
  theme: { type: String, enum: ['light', 'dark', 'system'], default: 'system' },
  language: { type: String, default: 'en' },
  notifications: { type: NotificationSettingsSchema, default: () => ({}) },
  accessibility: { type: AccessibilitySettingsSchema, default: () => ({}) },
  privacy: { type: PrivacySettingsSchema, default: () => ({}) }
}, { _id: false });

const UserStatsSchema = new Schema({
  totalQuizzesTaken: { type: Number, default: 0 },
  totalQuizzesCreated: { type: Number, default: 0 },
  averageScore: { type: Number, default: 0 },
  totalTimeSpent: { type: Number, default: 0 },
  streakDays: { type: Number, default: 0 },
  longestStreak: { type: Number, default: 0 },
  level: { type: Number, default: 1 },
  experience: { type: Number, default: 0 },
  ranking: { type: Number, default: 0 }
}, { _id: false });

const AchievementSchema = new Schema({
  _id: { type: Schema.Types.ObjectId, required: true },
  name: { type: String, required: true },
  description: { type: String, required: true },
  icon: { type: String, required: true },
  category: { 
    type: String, 
    enum: ['quiz_taking', 'quiz_creation', 'social', 'learning', 'streak', 'special'],
    required: true 
  },
  condition: {
    type: { type: String, enum: ['count', 'score', 'streak', 'time', 'special'], required: true },
    target: { type: Number, required: true },
    metric: { type: String, required: true }
  },
  points: { type: Number, required: true },
  rarity: { 
    type: String, 
    enum: ['common', 'uncommon', 'rare', 'epic', 'legendary'],
    required: true 
  },
  unlockedAt: { type: Date }
});

const UserSchema = new Schema({
  username: {
    type: String,
    required: [true, 'Username is required'],
    unique: true,
    trim: true,
    minlength: [3, 'Username must be at least 3 characters long'],
    maxlength: [30, 'Username cannot exceed 30 characters'],
    match: [/^[a-zA-Z0-9_]+$/, 'Username can only contain letters, numbers, and underscores']
  },
  email: {
    type: String,
    required: [true, 'Email is required'],
    unique: true,
    lowercase: true,
    trim: true,
    match: [/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/, 'Please enter a valid email']
  },
  password: {
    type: String,
    required: [true, 'Password is required'],
    minlength: [8, 'Password must be at least 8 characters long'],
    select: false
  },
  firstName: {
    type: String,
    required: [true, 'First name is required'],
    trim: true,
    maxlength: [50, 'First name cannot exceed 50 characters']
  },
  lastName: {
    type: String,
    required: [true, 'Last name is required'],
    trim: true,
    maxlength: [50, 'Last name cannot exceed 50 characters']
  },
  avatar: {
    type: String,
    default: null
  },
  role: {
    type: String,
    enum: Object.values(UserRole),
    default: UserRole.STUDENT
  },
  preferences: {
    type: UserPreferencesSchema,
    default: () => ({})
  },
  stats: {
    type: UserStatsSchema,
    default: () => ({})
  },
  achievements: [AchievementSchema],
  refreshTokens: [{
    type: String,
    select: false
  }],
  emailVerificationToken: {
    type: String,
    select: false
  },
  passwordResetToken: {
    type: String,
    select: false
  },
  passwordResetExpires: {
    type: Date,
    select: false
  },
  isEmailVerified: {
    type: Boolean,
    default: false
  },
  isActive: {
    type: Boolean,
    default: true
  },
  lastLoginAt: {
    type: Date
  }
}, {
  timestamps: true,
  toJSON: {
    transform: function(doc, ret) {
      delete ret.password;
      delete ret.refreshTokens;
      delete ret.emailVerificationToken;
      delete ret.passwordResetToken;
      delete ret.passwordResetExpires;
      delete ret.__v;
      return ret;
    }
  }
});

// Indexes
UserSchema.index({ email: 1 }, { unique: true });
UserSchema.index({ username: 1 }, { unique: true });
UserSchema.index({ 'stats.ranking': -1 });
UserSchema.index({ createdAt: -1 });

// Pre-save middleware to hash password
UserSchema.pre('save', async function(next) {
  if (!this.isModified('password')) return next();
  
  try {
    const salt = await bcrypt.genSalt(12);
    this.password = await bcrypt.hash(this.password, salt);
    next();
  } catch (error) {
    next(error as Error);
  }
});

// Method to compare password
UserSchema.methods.comparePassword = async function(candidatePassword: string): Promise<boolean> {
  return bcrypt.compare(candidatePassword, this.password);
};

// Method to generate password reset token
UserSchema.methods.generatePasswordResetToken = function(): string {
  const resetToken = Math.random().toString(36).substring(2, 15) + 
                    Math.random().toString(36).substring(2, 15);
  
  this.passwordResetToken = bcrypt.hashSync(resetToken, 10);
  this.passwordResetExpires = new Date(Date.now() + 10 * 60 * 1000); // 10 minutes
  
  return resetToken;
};

// Virtual for full name
UserSchema.virtual('fullName').get(function() {
  return `${this.firstName} ${this.lastName}`;
});

export const User = mongoose.model<UserDocument>('User', UserSchema);
