import mongoose, { Schema, Document } from 'mongoose';
import { QuizAttempt as IQuizAttempt, Answer } from '../../../shared/types';

export interface QuizAttemptDocument extends Omit<IQuizAttempt, '_id'>, Document {}

const AnswerSchema = new Schema({
  questionId: {
    type: Schema.Types.ObjectId,
    required: [true, 'Question ID is required']
  },
  answer: {
    type: Schema.Types.Mixed,
    required: [true, 'Answer is required']
  },
  isCorrect: {
    type: Boolean,
    required: [true, 'Correct status is required']
  },
  points: {
    type: Number,
    required: [true, 'Points are required'],
    min: [0, 'Points cannot be negative']
  },
  timeSpent: {
    type: Number,
    required: [true, 'Time spent is required'],
    min: [0, 'Time spent cannot be negative']
  }
}, { _id: false });

const QuizAttemptSchema = new Schema({
  quizId: {
    type: Schema.Types.ObjectId,
    ref: 'Quiz',
    required: [true, 'Quiz ID is required']
  },
  userId: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'User ID is required']
  },
  answers: {
    type: [AnswerSchema],
    default: []
  },
  score: {
    type: Number,
    required: [true, 'Score is required'],
    min: [0, 'Score cannot be negative']
  },
  maxScore: {
    type: Number,
    required: [true, 'Max score is required'],
    min: [1, 'Max score must be at least 1']
  },
  percentage: {
    type: Number,
    required: [true, 'Percentage is required'],
    min: [0, 'Percentage cannot be negative'],
    max: [100, 'Percentage cannot exceed 100']
  },
  timeSpent: {
    type: Number,
    required: [true, 'Time spent is required'],
    min: [0, 'Time spent cannot be negative']
  },
  startedAt: {
    type: Date,
    required: [true, 'Start time is required'],
    default: Date.now
  },
  completedAt: {
    type: Date
  },
  isCompleted: {
    type: Boolean,
    default: false
  },
  feedback: {
    type: String,
    trim: true,
    maxlength: [1000, 'Feedback cannot exceed 1000 characters']
  }
}, {
  timestamps: true,
  toJSON: {
    transform: function(doc, ret) {
      delete ret.__v;
      return ret;
    }
  }
});

// Indexes
QuizAttemptSchema.index({ userId: 1, quizId: 1 });
QuizAttemptSchema.index({ quizId: 1, completedAt: -1 });
QuizAttemptSchema.index({ userId: 1, completedAt: -1 });
QuizAttemptSchema.index({ userId: 1, isCompleted: 1 });

// Virtual for duration
QuizAttemptSchema.virtual('duration').get(function() {
  if (this.completedAt && this.startedAt) {
    return Math.floor((this.completedAt.getTime() - this.startedAt.getTime()) / 1000);
  }
  return 0;
});

// Virtual for grade
QuizAttemptSchema.virtual('grade').get(function() {
  if (this.percentage >= 90) return 'A';
  if (this.percentage >= 80) return 'B';
  if (this.percentage >= 70) return 'C';
  if (this.percentage >= 60) return 'D';
  return 'F';
});

// Pre-save middleware to calculate percentage
QuizAttemptSchema.pre('save', function(next) {
  if (this.isModified('score') || this.isModified('maxScore')) {
    this.percentage = Math.round((this.score / this.maxScore) * 100 * 100) / 100;
  }
  
  if (this.isModified('isCompleted') && this.isCompleted && !this.completedAt) {
    this.completedAt = new Date();
  }
  
  next();
});

// Method to add answer
QuizAttemptSchema.methods.addAnswer = function(answer: Answer) {
  const existingAnswerIndex = this.answers.findIndex(
    (a: Answer) => a.questionId.toString() === answer.questionId.toString()
  );
  
  if (existingAnswerIndex !== -1) {
    this.answers[existingAnswerIndex] = answer;
  } else {
    this.answers.push(answer);
  }
  
  // Recalculate score
  this.score = this.answers.reduce((total: number, ans: Answer) => total + ans.points, 0);
  this.timeSpent = this.answers.reduce((total: number, ans: Answer) => total + ans.timeSpent, 0);
};

// Method to complete attempt
QuizAttemptSchema.methods.complete = function() {
  this.isCompleted = true;
  this.completedAt = new Date();
  
  // Calculate final score and percentage
  this.score = this.answers.reduce((total: number, ans: Answer) => total + ans.points, 0);
  this.percentage = Math.round((this.score / this.maxScore) * 100 * 100) / 100;
};

// Method to check if attempt is expired
QuizAttemptSchema.methods.isExpired = function(timeLimit?: number): boolean {
  if (!timeLimit) return false;
  
  const now = new Date();
  const elapsed = Math.floor((now.getTime() - this.startedAt.getTime()) / 1000);
  
  return elapsed > timeLimit;
};

// Static method to get user's best attempt for a quiz
QuizAttemptSchema.statics.getBestAttempt = function(userId: string, quizId: string) {
  return this.findOne({
    userId,
    quizId,
    isCompleted: true
  }).sort({ score: -1, completedAt: -1 });
};

// Static method to get user's latest attempt for a quiz
QuizAttemptSchema.statics.getLatestAttempt = function(userId: string, quizId: string) {
  return this.findOne({
    userId,
    quizId
  }).sort({ startedAt: -1 });
};

// Static method to get user's attempt count for a quiz
QuizAttemptSchema.statics.getAttemptCount = function(userId: string, quizId: string) {
  return this.countDocuments({
    userId,
    quizId,
    isCompleted: true
  });
};

export const QuizAttempt = mongoose.model<QuizAttemptDocument>('QuizAttempt', QuizAttemptSchema);
