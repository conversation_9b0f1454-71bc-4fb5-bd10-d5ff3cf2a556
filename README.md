# 🧠 QuizCraft - AI-Powered Learning & Quiz Platform

[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![Node.js Version](https://img.shields.io/badge/node-%3E%3D18.0.0-brightgreen)](https://nodejs.org/)
[![React Version](https://img.shields.io/badge/react-%3E%3D18.0.0-blue)](https://reactjs.org/)
[![TypeScript](https://img.shields.io/badge/typescript-%3E%3D4.9.0-blue)](https://www.typescriptlang.org/)

## 🚀 Alternative Product Names & Domain Suggestions

**Primary Options:**
- **QuizCraft** - `quizcraft.ai` | `quizcraft.io` | `quizcraft.app`
- **LearnForge** - `learnforge.ai` | `learnforge.io` | `learnforge.app`
- **StudyMaster** - `studymaster.ai` | `studymaster.io` | `studymaster.app`
- **BrainCraft** - `braincraft.ai` | `braincraft.io` | `braincraft.app`
- **QuizGenius** - `quizgenius.ai` | `quizgenius.io` | `quizgenius.app`

**Creative Alternatives:**
- **Quizzly** - `quizzly.ai` | `quizzly.io`
- **StudyVault** - `studyvault.ai` | `studyvault.io`
- **MindQuest** - `mindquest.ai` | `mindquest.io`
- **LearnLab** - `learnlab.ai` | `learnlab.io`
- **QuizFlow** - `quizflow.ai` | `quizflow.io`

## 📋 Overview

QuizCraft is a comprehensive AI-powered quiz and learning platform that revolutionizes how people create, share, and engage with educational content. Built with cutting-edge technology, it offers intelligent quiz generation, real-time multiplayer competitions, and personalized learning experiences.

## ✨ Key Features

### 🎯 Core Functionality
- **Multi-Modal Quiz Creation**: Support for text, PDF, images, audio, video, and URL content
- **AI-Powered Question Generation**: Intelligent quiz creation using OpenAI GPT-4
- **Real-Time Multiplayer**: Live quiz competitions with Socket.io
- **Adaptive Learning**: Personalized difficulty adjustment and spaced repetition
- **Advanced Analytics**: Comprehensive performance tracking and insights

### 🎮 Gamification & Social
- **Achievement System**: Badges, streaks, and milestone rewards
- **Leaderboards**: Global and category-specific rankings
- **Study Groups**: Collaborative learning environments
- **Peer Sharing**: Community-driven content sharing
- **Discussion Forums**: Interactive learning discussions

### 🔧 Technical Features
- **OCR Processing**: Extract text from images and documents
- **Audio Transcription**: Convert speech to text for quiz creation
- **Accessibility**: WCAG 2.1 compliant with screen reader support
- **Progressive Web App**: Mobile-optimized experience
- **Offline Support**: Continue learning without internet

## 🏗 System Architecture

```mermaid
graph TB
    subgraph "Client Layer"
        WEB[Web App - React/TypeScript]
        MOB[Mobile App - React Native]
        PWA[Progressive Web App]
    end
    
    subgraph "API Gateway & Load Balancer"
        LB[Load Balancer - Nginx]
        API[API Gateway - Express.js]
    end
    
    subgraph "Authentication & Security"
        AUTH[JWT Auth Service]
        OAUTH[OAuth Providers]
        RBAC[Role-Based Access Control]
    end
    
    subgraph "Core Services"
        USER[User Management Service]
        QUIZ[Quiz Management Service]
        AI[AI Quiz Generation Service]
        MULTI[Multiplayer Service - Socket.io]
        ANALYTICS[Analytics Service]
        NOTIF[Notification Service]
    end
    
    subgraph "AI & ML Layer"
        GPT[OpenAI GPT-4]
        OCR[OCR Processing]
        SPEECH[Speech-to-Text]
        ADAPTIVE[Adaptive Learning Algorithm]
        SPACED[Spaced Repetition Engine]
    end
    
    subgraph "Data Layer"
        MONGO[(MongoDB - Primary DB)]
        REDIS[(Redis - Cache & Sessions)]
        ELASTIC[(Elasticsearch - Search)]
        S3[(AWS S3 - File Storage)]
    end
    
    WEB --> LB
    MOB --> LB
    PWA --> LB
    LB --> API
    API --> AUTH
    API --> USER
    API --> QUIZ
    API --> AI
    API --> MULTI
    API --> ANALYTICS
```

## 🔄 Data Flow & User Journey

```mermaid
sequenceDiagram
    participant U as User
    participant F as Frontend
    participant A as API Gateway
    participant Auth as Auth Service
    participant Q as Quiz Service
    participant AI as AI Service
    participant DB as Database
    
    U->>F: Create new quiz
    F->>A: POST /quiz/create
    A->>Auth: Validate token
    Auth-->>A: User authorized
    A->>Q: Process quiz data
    Q->>AI: Generate questions
    AI-->>Q: Generated questions
    Q->>DB: Save quiz
    DB-->>Q: Quiz saved
    Q-->>A: Quiz created
    A-->>F: Quiz response
    F-->>U: Quiz ready
```

## 🛠 Tech Stack

### Frontend
- **React 18+** with TypeScript
- **Tailwind CSS** for styling
- **Framer Motion** for animations
- **React Query** for state management
- **React Hook Form** for form handling

### Backend
- **Node.js** with Express.js
- **MongoDB** with Mongoose ODM
- **Redis** for caching and sessions
- **Socket.io** for real-time features
- **JWT** authentication with refresh tokens

### AI & ML
- **OpenAI GPT-4** for quiz generation
- **Tesseract.js** for OCR processing
- **Web Speech API** for audio transcription
- **Custom algorithms** for adaptive learning

### DevOps & Deployment
- **Docker** containerization
- **GitHub Actions** CI/CD
- **AWS/Vercel** deployment
- **MongoDB Atlas** database hosting

## 📁 Project Structure

```
quizcraft/
├── client/                 # React frontend
│   ├── public/
│   ├── src/
│   │   ├── components/     # Reusable UI components
│   │   ├── pages/         # Page components
│   │   ├── hooks/         # Custom React hooks
│   │   ├── services/      # API services
│   │   ├── utils/         # Utility functions
│   │   ├── types/         # TypeScript definitions
│   │   └── styles/        # Global styles
│   ├── package.json
│   └── tsconfig.json
├── server/                # Node.js backend
│   ├── src/
│   │   ├── controllers/   # Route controllers
│   │   ├── models/        # Database models
│   │   ├── middleware/    # Express middleware
│   │   ├── services/      # Business logic
│   │   ├── routes/        # API routes
│   │   ├── utils/         # Utility functions
│   │   └── config/        # Configuration files
│   ├── package.json
│   └── tsconfig.json
├── shared/                # Shared types and utilities
├── docs/                  # Documentation
├── tests/                 # Test files
├── docker-compose.yml     # Docker configuration
├── .github/               # GitHub Actions workflows
└── README.md
```

## 🚀 Quick Start

### Prerequisites
- Node.js 18+ and npm/yarn
- MongoDB 5.0+
- Redis 6.0+
- OpenAI API key

### Installation

1. **Clone the repository**
```bash
git clone https://github.com/yourusername/quizcraft.git
cd quizcraft
```

2. **Install dependencies**
```bash
# Install server dependencies
cd server && npm install

# Install client dependencies
cd ../client && npm install
```

3. **Environment Setup**
```bash
# Copy environment files
cp server/.env.example server/.env
cp client/.env.example client/.env

# Configure your environment variables
```

4. **Start development servers**
```bash
# Start backend (from server directory)
npm run dev

# Start frontend (from client directory)
npm run dev
```

5. **Access the application**
- Frontend: http://localhost:3000
- Backend API: http://localhost:5000
- API Documentation: http://localhost:5000/api-docs

## 🧪 Testing

```bash
# Run all tests
npm run test

# Run tests with coverage
npm run test:coverage

# Run E2E tests
npm run test:e2e
```

## 📚 API Documentation

### Authentication Endpoints
- `POST /api/auth/register` - User registration
- `POST /api/auth/login` - User login
- `POST /api/auth/refresh` - Refresh access token
- `POST /api/auth/logout` - User logout

### Quiz Management
- `GET /api/quizzes` - Get all quizzes
- `POST /api/quizzes` - Create new quiz
- `GET /api/quizzes/:id` - Get quiz by ID
- `PUT /api/quizzes/:id` - Update quiz
- `DELETE /api/quizzes/:id` - Delete quiz

### AI Generation
- `POST /api/ai/generate-quiz` - Generate quiz from content
- `POST /api/ai/process-file` - Process uploaded file
- `POST /api/ai/transcribe-audio` - Transcribe audio to text

## 🔒 Security Features

- JWT authentication with refresh tokens
- Rate limiting and request throttling
- Input validation and sanitization
- CORS configuration
- Helmet.js security headers
- MongoDB injection prevention
- XSS protection

## 🌐 Deployment

### Docker Deployment
```bash
# Build and run with Docker Compose
docker-compose up --build
```

### Production Deployment
```bash
# Build for production
npm run build

# Start production server
npm run start
```

## 🤝 Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit your changes (`git commit -m 'Add some AmazingFeature'`)
4. Push to the branch (`git push origin feature/AmazingFeature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 👥 Team

- **Lead Developer**: [Your Name](https://github.com/yourusername)
- **UI/UX Designer**: [Designer Name](https://github.com/designer)
- **DevOps Engineer**: [DevOps Name](https://github.com/devops)

## 🙏 Acknowledgments

- OpenAI for GPT-4 API
- MongoDB for database solutions
- Vercel for deployment platform
- All contributors and beta testers

---

**Made with ❤️ by the QuizCraft Team**
