name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

env:
  NODE_VERSION: '18'
  MONGODB_VERSION: '7.0'
  REDIS_VERSION: '7.2'

jobs:
  # Test Backend
  test-backend:
    runs-on: ubuntu-latest
    
    services:
      mongodb:
        image: mongo:7.0
        env:
          MONGO_INITDB_ROOT_USERNAME: admin
          MONGO_INITDB_ROOT_PASSWORD: password
        ports:
          - 27017:27017
        options: >-
          --health-cmd "mongosh --eval 'db.adminCommand(\"ping\")'"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
      
      redis:
        image: redis:7.2-alpine
        ports:
          - 6379:6379
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: server/package-lock.json

    - name: Install backend dependencies
      working-directory: ./server
      run: npm ci

    - name: Run backend linting
      working-directory: ./server
      run: npm run lint

    - name: Run backend tests
      working-directory: ./server
      run: npm run test:coverage
      env:
        NODE_ENV: test
        MONGODB_URI: ************************************************************************
        REDIS_URL: redis://localhost:6379
        JWT_SECRET: test-jwt-secret
        JWT_REFRESH_SECRET: test-refresh-secret

    - name: Upload backend coverage
      uses: codecov/codecov-action@v3
      with:
        file: ./server/coverage/lcov.info
        flags: backend

  # Test Frontend
  test-frontend:
    runs-on: ubuntu-latest

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: client/package-lock.json

    - name: Install frontend dependencies
      working-directory: ./client
      run: npm ci

    - name: Run frontend linting
      working-directory: ./client
      run: npm run lint

    - name: Run frontend type checking
      working-directory: ./client
      run: npm run type-check

    - name: Run frontend tests
      working-directory: ./client
      run: npm run test:coverage

    - name: Upload frontend coverage
      uses: codecov/codecov-action@v3
      with:
        file: ./client/coverage/lcov.info
        flags: frontend

  # E2E Tests
  e2e-tests:
    runs-on: ubuntu-latest
    needs: [test-backend, test-frontend]

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: Start services
      run: |
        docker-compose -f docker-compose.test.yml up -d
        sleep 30

    - name: Run E2E tests
      run: npm run test:e2e

    - name: Stop services
      run: docker-compose -f docker-compose.test.yml down

  # Build and Deploy
  build-and-deploy:
    runs-on: ubuntu-latest
    needs: [test-backend, test-frontend, e2e-tests]
    if: github.ref == 'refs/heads/main'

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'

    - name: Build backend
      working-directory: ./server
      run: |
        npm ci
        npm run build

    - name: Build frontend
      working-directory: ./client
      run: |
        npm ci
        npm run build

    - name: Build Docker images
      run: |
        docker build -t quizcraft-server:latest ./server
        docker build -t quizcraft-client:latest ./client

    - name: Deploy to staging
      if: github.ref == 'refs/heads/develop'
      run: |
        echo "Deploy to staging environment"
        # Add staging deployment commands here

    - name: Deploy to production
      if: github.ref == 'refs/heads/main'
      run: |
        echo "Deploy to production environment"
        # Add production deployment commands here

  # Security Scan
  security-scan:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Run Trivy vulnerability scanner
      uses: aquasecurity/trivy-action@master
      with:
        scan-type: 'fs'
        scan-ref: '.'
        format: 'sarif'
        output: 'trivy-results.sarif'

    - name: Upload Trivy scan results
      uses: github/codeql-action/upload-sarif@v2
      with:
        sarif_file: 'trivy-results.sarif'
