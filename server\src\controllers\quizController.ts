import { Request, Response } from 'express';
import { validationResult } from 'express-validator';
import { Quiz } from '../models/Quiz';
import { QuizAttempt } from '../models/QuizAttempt';
import { Analytics } from '../models/Analytics';
import { logger } from '../config/logger';
import { asyncHandler, createError } from '../middleware/errorHandler';

/**
 * Get all quizzes with pagination and filtering
 */
export const getQuizzes = asyncHandler(async (req: Request, res: Response) => {
  const page = parseInt(req.query.page as string) || 1;
  const limit = parseInt(req.query.limit as string) || 10;
  const skip = (page - 1) * limit;
  
  const {
    category,
    difficulty,
    tags,
    q: searchQuery,
    sort = '-createdAt'
  } = req.query;

  // Build filter object
  const filter: any = { isActive: true };
  
  // Only show public quizzes unless user is authenticated and owns them
  if (!req.user) {
    filter.isPublic = true;
  } else {
    filter.$or = [
      { isPublic: true },
      { creator: req.user._id }
    ];
  }

  if (category) filter.category = category;
  if (difficulty) filter.difficulty = difficulty;
  if (tags) filter.tags = { $in: Array.isArray(tags) ? tags : [tags] };

  // Text search
  if (searchQuery) {
    filter.$text = { $search: searchQuery as string };
  }

  // Execute query
  const [quizzes, total] = await Promise.all([
    Quiz.find(filter)
      .populate('creator', 'username firstName lastName avatar')
      .select('-questions.correctAnswer -questions.explanation')
      .sort(sort as string)
      .skip(skip)
      .limit(limit),
    Quiz.countDocuments(filter)
  ]);

  const totalPages = Math.ceil(total / limit);

  res.json({
    success: true,
    data: quizzes,
    pagination: {
      page,
      limit,
      total,
      pages: totalPages,
      hasNext: page < totalPages,
      hasPrev: page > 1
    }
  });
});

/**
 * Get quiz by ID
 */
export const getQuizById = asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;
  const userId = req.user?._id;

  const quiz = await Quiz.findById(id)
    .populate('creator', 'username firstName lastName avatar');

  if (!quiz) {
    throw createError('Quiz not found', 404);
  }

  // Check if user can access this quiz
  if (!quiz.isPublic && (!userId || quiz.creator._id.toString() !== userId.toString())) {
    throw createError('Access denied', 403);
  }

  if (!quiz.isActive) {
    throw createError('Quiz is not available', 404);
  }

  // Check if quiz is available based on time settings
  if (!quiz.isAvailable()) {
    throw createError('Quiz is not currently available', 403);
  }

  // Remove correct answers and explanations for non-owners
  let responseQuiz = quiz.toJSON();
  if (!userId || quiz.creator._id.toString() !== userId.toString()) {
    responseQuiz.questions = responseQuiz.questions.map((q: any) => {
      const { correctAnswer, explanation, ...question } = q;
      return question;
    });
  }

  res.json({
    success: true,
    data: responseQuiz
  });
});

/**
 * Create new quiz
 */
export const createQuiz = asyncHandler(async (req: Request, res: Response) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      error: 'Validation failed',
      details: errors.array()
    });
  }

  const quizData = {
    ...req.body,
    creator: req.user._id
  };

  const quiz = new Quiz(quizData);
  await quiz.save();

  // Populate creator info
  await quiz.populate('creator', 'username firstName lastName avatar');

  // Track analytics
  await Analytics.create({
    userId: req.user._id,
    type: 'quiz_creation',
    data: {
      quizId: quiz._id,
      category: quiz.category,
      difficulty: quiz.difficulty,
      questionCount: quiz.questions.length
    }
  });

  logger.info(`Quiz created: ${quiz._id} by user: ${req.user._id}`);

  res.status(201).json({
    success: true,
    data: quiz,
    message: 'Quiz created successfully'
  });
});

/**
 * Update quiz
 */
export const updateQuiz = asyncHandler(async (req: Request, res: Response) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      error: 'Validation failed',
      details: errors.array()
    });
  }

  const { id } = req.params;
  const quiz = await Quiz.findById(id);

  if (!quiz) {
    throw createError('Quiz not found', 404);
  }

  // Check ownership
  if (quiz.creator.toString() !== req.user._id.toString() && req.user.role !== 'admin') {
    throw createError('Access denied', 403);
  }

  // Update quiz
  Object.assign(quiz, req.body);
  await quiz.save();

  await quiz.populate('creator', 'username firstName lastName avatar');

  logger.info(`Quiz updated: ${quiz._id} by user: ${req.user._id}`);

  res.json({
    success: true,
    data: quiz,
    message: 'Quiz updated successfully'
  });
});

/**
 * Delete quiz
 */
export const deleteQuiz = asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;
  const quiz = await Quiz.findById(id);

  if (!quiz) {
    throw createError('Quiz not found', 404);
  }

  // Check ownership
  if (quiz.creator.toString() !== req.user._id.toString() && req.user.role !== 'admin') {
    throw createError('Access denied', 403);
  }

  // Soft delete by setting isActive to false
  quiz.isActive = false;
  await quiz.save();

  logger.info(`Quiz deleted: ${quiz._id} by user: ${req.user._id}`);

  res.json({
    success: true,
    message: 'Quiz deleted successfully'
  });
});

/**
 * Get user's quizzes
 */
export const getUserQuizzes = asyncHandler(async (req: Request, res: Response) => {
  const page = parseInt(req.query.page as string) || 1;
  const limit = parseInt(req.query.limit as string) || 10;
  const skip = (page - 1) * limit;

  const filter = {
    creator: req.user._id,
    isActive: true
  };

  const [quizzes, total] = await Promise.all([
    Quiz.find(filter)
      .sort('-createdAt')
      .skip(skip)
      .limit(limit),
    Quiz.countDocuments(filter)
  ]);

  const totalPages = Math.ceil(total / limit);

  res.json({
    success: true,
    data: quizzes,
    pagination: {
      page,
      limit,
      total,
      pages: totalPages,
      hasNext: page < totalPages,
      hasPrev: page > 1
    }
  });
});

/**
 * Get quiz statistics
 */
export const getQuizStats = asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;
  const quiz = await Quiz.findById(id);

  if (!quiz) {
    throw createError('Quiz not found', 404);
  }

  // Check ownership
  if (quiz.creator.toString() !== req.user._id.toString() && req.user.role !== 'admin') {
    throw createError('Access denied', 403);
  }

  // Get detailed statistics
  const stats = await QuizAttempt.aggregate([
    { $match: { quizId: quiz._id, isCompleted: true } },
    {
      $group: {
        _id: null,
        totalAttempts: { $sum: 1 },
        averageScore: { $avg: '$percentage' },
        averageTimeSpent: { $avg: '$timeSpent' },
        highestScore: { $max: '$percentage' },
        lowestScore: { $min: '$percentage' },
        uniqueUsers: { $addToSet: '$userId' }
      }
    }
  ]);

  const result = stats[0] || {
    totalAttempts: 0,
    averageScore: 0,
    averageTimeSpent: 0,
    highestScore: 0,
    lowestScore: 0,
    uniqueUsers: []
  };

  result.uniqueUserCount = result.uniqueUsers.length;
  delete result.uniqueUsers;

  res.json({
    success: true,
    data: {
      quiz: {
        id: quiz._id,
        title: quiz.title,
        category: quiz.category,
        difficulty: quiz.difficulty
      },
      stats: result
    }
  });
});
