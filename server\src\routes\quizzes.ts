import { Router } from 'express';
import {
  getQuizzes,
  getQuizById,
  createQuiz,
  updateQuiz,
  deleteQuiz,
  getUserQuizzes,
  getQuizStats
} from '../controllers/quizController';
import {
  validateQuizCreate,
  validateQuizUpdate,
  validateObjectId,
  validatePagination,
  validateSearch
} from '../middleware/validation';
import { authenticate, optionalAuth, authorize, checkOwnership } from '../middleware/auth';
import { UserRole } from '../../../shared/types';

const router = Router();

/**
 * @route   GET /api/quizzes
 * @desc    Get all public quizzes with pagination and filtering
 * @access  Public (with optional auth for personalized results)
 */
router.get('/', optionalAuth, validatePagination, validateSearch, getQuizzes);

/**
 * @route   GET /api/quizzes/my
 * @desc    Get current user's quizzes
 * @access  Private
 */
router.get('/my', authenticate, validatePagination, getUserQuizzes);

/**
 * @route   GET /api/quizzes/:id
 * @desc    Get quiz by ID
 * @access  Public (for public quizzes) / Private (for private quizzes)
 */
router.get('/:id', optionalAuth, validateObjectId('id'), getQuizById);

/**
 * @route   POST /api/quizzes
 * @desc    Create new quiz
 * @access  Private
 */
router.post('/', authenticate, validateQuizCreate, createQuiz);

/**
 * @route   PUT /api/quizzes/:id
 * @desc    Update quiz
 * @access  Private (Owner or Admin)
 */
router.put('/:id', authenticate, validateObjectId('id'), validateQuizUpdate, updateQuiz);

/**
 * @route   DELETE /api/quizzes/:id
 * @desc    Delete quiz (soft delete)
 * @access  Private (Owner or Admin)
 */
router.delete('/:id', authenticate, validateObjectId('id'), deleteQuiz);

/**
 * @route   GET /api/quizzes/:id/stats
 * @desc    Get quiz statistics
 * @access  Private (Owner or Admin)
 */
router.get('/:id/stats', authenticate, validateObjectId('id'), getQuizStats);

export default router;
