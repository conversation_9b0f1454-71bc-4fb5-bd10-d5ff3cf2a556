import mongoose, { Schema, Document } from 'mongoose';
import { Analytics as IAnalytics, AnalyticsType } from '../../../shared/types';

export interface AnalyticsDocument extends Omit<IAnalytics, '_id'>, Document {}

const AnalyticsSchema = new Schema({
  userId: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'User ID is required']
  },
  type: {
    type: String,
    enum: Object.values(AnalyticsType),
    required: [true, 'Analytics type is required']
  },
  data: {
    type: Schema.Types.Mixed,
    required: [true, 'Analytics data is required']
  },
  timestamp: {
    type: Date,
    default: Date.now,
    required: true
  }
}, {
  timestamps: false,
  toJSON: {
    transform: function(doc, ret) {
      delete ret.__v;
      return ret;
    }
  }
});

// Indexes
AnalyticsSchema.index({ userId: 1, timestamp: -1 });
AnalyticsSchema.index({ type: 1, timestamp: -1 });
AnalyticsSchema.index({ userId: 1, type: 1, timestamp: -1 });

// TTL index to automatically delete old analytics data after 1 year
AnalyticsSchema.index({ timestamp: 1 }, { expireAfterSeconds: 31536000 });

// Static method to track quiz attempt
AnalyticsSchema.statics.trackQuizAttempt = function(userId: string, data: any) {
  return this.create({
    userId,
    type: AnalyticsType.QUIZ_ATTEMPT,
    data,
    timestamp: new Date()
  });
};

// Static method to track question answer
AnalyticsSchema.statics.trackQuestionAnswer = function(userId: string, data: any) {
  return this.create({
    userId,
    type: AnalyticsType.QUESTION_ANSWER,
    data,
    timestamp: new Date()
  });
};

// Static method to track time spent
AnalyticsSchema.statics.trackTimeSpent = function(userId: string, data: any) {
  return this.create({
    userId,
    type: AnalyticsType.TIME_SPENT,
    data,
    timestamp: new Date()
  });
};

// Static method to get user analytics by type
AnalyticsSchema.statics.getUserAnalytics = function(
  userId: string, 
  type?: AnalyticsType, 
  startDate?: Date, 
  endDate?: Date
) {
  const query: any = { userId };
  
  if (type) {
    query.type = type;
  }
  
  if (startDate || endDate) {
    query.timestamp = {};
    if (startDate) query.timestamp.$gte = startDate;
    if (endDate) query.timestamp.$lte = endDate;
  }
  
  return this.find(query).sort({ timestamp: -1 });
};

// Static method to get aggregated analytics
AnalyticsSchema.statics.getAggregatedAnalytics = function(
  userId: string,
  type: AnalyticsType,
  groupBy: 'day' | 'week' | 'month' = 'day',
  limit: number = 30
) {
  const groupFormat = {
    day: { $dateToString: { format: '%Y-%m-%d', date: '$timestamp' } },
    week: { $dateToString: { format: '%Y-%U', date: '$timestamp' } },
    month: { $dateToString: { format: '%Y-%m', date: '$timestamp' } }
  };
  
  return this.aggregate([
    {
      $match: {
        userId: new mongoose.Types.ObjectId(userId),
        type
      }
    },
    {
      $group: {
        _id: groupFormat[groupBy],
        count: { $sum: 1 },
        data: { $push: '$data' }
      }
    },
    {
      $sort: { _id: -1 }
    },
    {
      $limit: limit
    }
  ]);
};

export const Analytics = mongoose.model<AnalyticsDocument>('Analytics', AnalyticsSchema);
