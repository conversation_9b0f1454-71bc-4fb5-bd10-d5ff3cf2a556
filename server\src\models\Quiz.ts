import mongoose, { Schema, Document } from 'mongoose';
import { 
  Quiz as IQuiz, 
  Question, 
  QuestionType, 
  DifficultyLevel, 
  QuizSettings, 
  QuizStats,
  MediaContent 
} from '../../../shared/types';

export interface QuizDocument extends Omit<IQuiz, '_id'>, Document {}

const MediaContentSchema = new Schema({
  type: { type: String, enum: ['image', 'audio', 'video'], required: true },
  url: { type: String, required: true },
  alt: { type: String },
  duration: { type: Number }
}, { _id: false });

const QuestionSchema = new Schema({
  type: {
    type: String,
    enum: Object.values(QuestionType),
    required: [true, 'Question type is required']
  },
  question: {
    type: String,
    required: [true, 'Question text is required'],
    trim: true,
    maxlength: [1000, 'Question cannot exceed 1000 characters']
  },
  options: [{
    type: String,
    trim: true,
    maxlength: [200, 'Option cannot exceed 200 characters']
  }],
  correctAnswer: {
    type: Schema.Types.Mixed,
    required: [true, 'Correct answer is required']
  },
  explanation: {
    type: String,
    trim: true,
    maxlength: [500, 'Explanation cannot exceed 500 characters']
  },
  points: {
    type: Number,
    required: [true, 'Points are required'],
    min: [1, 'Points must be at least 1'],
    max: [100, 'Points cannot exceed 100']
  },
  timeLimit: {
    type: Number,
    min: [5, 'Time limit must be at least 5 seconds'],
    max: [300, 'Time limit cannot exceed 300 seconds']
  },
  difficulty: {
    type: String,
    enum: Object.values(DifficultyLevel),
    required: [true, 'Difficulty level is required']
  },
  tags: [{
    type: String,
    trim: true,
    maxlength: [30, 'Tag cannot exceed 30 characters']
  }],
  media: MediaContentSchema,
  hints: [{
    type: String,
    trim: true,
    maxlength: [200, 'Hint cannot exceed 200 characters']
  }]
});

const QuizSettingsSchema = new Schema({
  timeLimit: {
    type: Number,
    min: [60, 'Time limit must be at least 60 seconds']
  },
  allowRetakes: { type: Boolean, default: true },
  shuffleQuestions: { type: Boolean, default: false },
  shuffleAnswers: { type: Boolean, default: false },
  showCorrectAnswers: { type: Boolean, default: true },
  showExplanations: { type: Boolean, default: true },
  passingScore: {
    type: Number,
    min: [0, 'Passing score cannot be negative'],
    max: [100, 'Passing score cannot exceed 100']
  },
  maxAttempts: {
    type: Number,
    min: [1, 'Max attempts must be at least 1']
  },
  availableFrom: { type: Date },
  availableUntil: { type: Date }
}, { _id: false });

const QuizStatsSchema = new Schema({
  totalAttempts: { type: Number, default: 0 },
  averageScore: { type: Number, default: 0 },
  averageTimeSpent: { type: Number, default: 0 },
  completionRate: { type: Number, default: 0 },
  difficulty: { type: Number, default: 0 },
  popularity: { type: Number, default: 0 }
}, { _id: false });

const QuizSchema = new Schema({
  title: {
    type: String,
    required: [true, 'Quiz title is required'],
    trim: true,
    minlength: [3, 'Title must be at least 3 characters long'],
    maxlength: [100, 'Title cannot exceed 100 characters']
  },
  description: {
    type: String,
    required: [true, 'Quiz description is required'],
    trim: true,
    maxlength: [500, 'Description cannot exceed 500 characters']
  },
  category: {
    type: String,
    required: [true, 'Category is required'],
    trim: true,
    maxlength: [50, 'Category cannot exceed 50 characters']
  },
  tags: [{
    type: String,
    trim: true,
    maxlength: [30, 'Tag cannot exceed 30 characters']
  }],
  difficulty: {
    type: String,
    enum: Object.values(DifficultyLevel),
    required: [true, 'Difficulty level is required']
  },
  questions: {
    type: [QuestionSchema],
    required: [true, 'Quiz must have at least one question'],
    validate: {
      validator: function(questions: Question[]) {
        return questions.length > 0;
      },
      message: 'Quiz must have at least one question'
    }
  },
  creator: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'Creator is required']
  },
  settings: {
    type: QuizSettingsSchema,
    default: () => ({})
  },
  stats: {
    type: QuizStatsSchema,
    default: () => ({})
  },
  isPublic: {
    type: Boolean,
    default: true
  },
  isActive: {
    type: Boolean,
    default: true
  }
}, {
  timestamps: true,
  toJSON: {
    transform: function(doc, ret) {
      delete ret.__v;
      return ret;
    }
  }
});

// Indexes
QuizSchema.index({ creator: 1 });
QuizSchema.index({ category: 1 });
QuizSchema.index({ tags: 1 });
QuizSchema.index({ difficulty: 1 });
QuizSchema.index({ isPublic: 1, isActive: 1 });
QuizSchema.index({ 'stats.popularity': -1 });
QuizSchema.index({ createdAt: -1 });

// Text search index
QuizSchema.index({
  title: 'text',
  description: 'text',
  category: 'text',
  tags: 'text'
});

// Virtual for total points
QuizSchema.virtual('totalPoints').get(function() {
  return this.questions.reduce((total: number, question: Question) => total + question.points, 0);
});

// Virtual for estimated time
QuizSchema.virtual('estimatedTime').get(function() {
  const questionTime = this.questions.reduce((total: number, question: Question) => {
    return total + (question.timeLimit || 30);
  }, 0);
  
  return this.settings.timeLimit || questionTime;
});

// Pre-save middleware to validate quiz settings
QuizSchema.pre('save', function(next) {
  // Validate available dates
  if (this.settings.availableFrom && this.settings.availableUntil) {
    if (this.settings.availableFrom >= this.settings.availableUntil) {
      return next(new Error('Available from date must be before available until date'));
    }
  }
  
  // Validate question types and options
  for (const question of this.questions) {
    if (question.type === QuestionType.MULTIPLE_CHOICE && (!question.options || question.options.length < 2)) {
      return next(new Error('Multiple choice questions must have at least 2 options'));
    }
    
    if (question.type === QuestionType.TRUE_FALSE && (!question.options || question.options.length !== 2)) {
      return next(new Error('True/false questions must have exactly 2 options'));
    }
  }
  
  next();
});

// Method to check if quiz is available
QuizSchema.methods.isAvailable = function(): boolean {
  if (!this.isActive) return false;
  
  const now = new Date();
  
  if (this.settings.availableFrom && now < this.settings.availableFrom) {
    return false;
  }
  
  if (this.settings.availableUntil && now > this.settings.availableUntil) {
    return false;
  }
  
  return true;
};

// Method to calculate difficulty score
QuizSchema.methods.calculateDifficulty = function(): number {
  const difficultyMap = {
    [DifficultyLevel.BEGINNER]: 1,
    [DifficultyLevel.INTERMEDIATE]: 2,
    [DifficultyLevel.ADVANCED]: 3,
    [DifficultyLevel.EXPERT]: 4
  };
  
  const avgDifficulty = this.questions.reduce((total: number, question: Question) => {
    return total + difficultyMap[question.difficulty];
  }, 0) / this.questions.length;
  
  return Math.round(avgDifficulty * 100) / 100;
};

export const Quiz = mongoose.model<QuizDocument>('Quiz', QuizSchema);
