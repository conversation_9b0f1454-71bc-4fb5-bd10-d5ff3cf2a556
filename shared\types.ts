// Shared types between client and server

export interface User {
  _id: string;
  username: string;
  email: string;
  firstName: string;
  lastName: string;
  avatar?: string;
  role: UserRole;
  preferences: UserPreferences;
  stats: UserStats;
  achievements: Achievement[];
  createdAt: Date;
  updatedAt: Date;
  lastLoginAt?: Date;
  isEmailVerified: boolean;
  isActive: boolean;
}

export enum UserRole {
  STUDENT = 'student',
  TEACHER = 'teacher',
  ADMIN = 'admin',
  MODERATOR = 'moderator'
}

export interface UserPreferences {
  theme: 'light' | 'dark' | 'system';
  language: string;
  notifications: NotificationSettings;
  accessibility: AccessibilitySettings;
  privacy: PrivacySettings;
}

export interface NotificationSettings {
  email: boolean;
  push: boolean;
  sms: boolean;
  quizReminders: boolean;
  achievementAlerts: boolean;
  studyGroupUpdates: boolean;
}

export interface AccessibilitySettings {
  fontSize: 'small' | 'medium' | 'large';
  highContrast: boolean;
  screenReader: boolean;
  dyslexiaFriendly: boolean;
  reducedMotion: boolean;
}

export interface PrivacySettings {
  profileVisibility: 'public' | 'friends' | 'private';
  showStats: boolean;
  showAchievements: boolean;
  allowFriendRequests: boolean;
}

export interface UserStats {
  totalQuizzesTaken: number;
  totalQuizzesCreated: number;
  averageScore: number;
  totalTimeSpent: number; // in minutes
  streakDays: number;
  longestStreak: number;
  level: number;
  experience: number;
  ranking: number;
}

export interface Quiz {
  _id: string;
  title: string;
  description: string;
  category: string;
  tags: string[];
  difficulty: DifficultyLevel;
  questions: Question[];
  creator: string; // User ID
  settings: QuizSettings;
  stats: QuizStats;
  isPublic: boolean;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export enum DifficultyLevel {
  BEGINNER = 'beginner',
  INTERMEDIATE = 'intermediate',
  ADVANCED = 'advanced',
  EXPERT = 'expert'
}

export interface QuizSettings {
  timeLimit?: number; // in seconds
  allowRetakes: boolean;
  shuffleQuestions: boolean;
  shuffleAnswers: boolean;
  showCorrectAnswers: boolean;
  showExplanations: boolean;
  passingScore?: number; // percentage
  maxAttempts?: number;
  availableFrom?: Date;
  availableUntil?: Date;
}

export interface QuizStats {
  totalAttempts: number;
  averageScore: number;
  averageTimeSpent: number;
  completionRate: number;
  difficulty: number; // calculated difficulty
  popularity: number;
}

export interface Question {
  _id: string;
  type: QuestionType;
  question: string;
  options?: string[];
  correctAnswer: string | string[];
  explanation?: string;
  points: number;
  timeLimit?: number;
  difficulty: DifficultyLevel;
  tags: string[];
  media?: MediaContent;
  hints?: string[];
}

export enum QuestionType {
  MULTIPLE_CHOICE = 'multiple_choice',
  TRUE_FALSE = 'true_false',
  SHORT_ANSWER = 'short_answer',
  ESSAY = 'essay',
  FILL_IN_BLANK = 'fill_in_blank',
  MATCHING = 'matching',
  ORDERING = 'ordering',
  DRAG_DROP = 'drag_drop'
}

export interface MediaContent {
  type: 'image' | 'audio' | 'video';
  url: string;
  alt?: string;
  duration?: number; // for audio/video
}

export interface QuizAttempt {
  _id: string;
  quizId: string;
  userId: string;
  answers: Answer[];
  score: number;
  maxScore: number;
  percentage: number;
  timeSpent: number; // in seconds
  startedAt: Date;
  completedAt?: Date;
  isCompleted: boolean;
  feedback?: string;
}

export interface Answer {
  questionId: string;
  answer: string | string[];
  isCorrect: boolean;
  points: number;
  timeSpent: number;
}

export interface StudyGroup {
  _id: string;
  name: string;
  description: string;
  creator: string; // User ID
  members: string[]; // User IDs
  moderators: string[]; // User IDs
  quizzes: string[]; // Quiz IDs
  isPublic: boolean;
  maxMembers?: number;
  createdAt: Date;
  updatedAt: Date;
}

export interface Achievement {
  _id: string;
  name: string;
  description: string;
  icon: string;
  category: AchievementCategory;
  condition: AchievementCondition;
  points: number;
  rarity: AchievementRarity;
  unlockedAt?: Date;
}

export enum AchievementCategory {
  QUIZ_TAKING = 'quiz_taking',
  QUIZ_CREATION = 'quiz_creation',
  SOCIAL = 'social',
  LEARNING = 'learning',
  STREAK = 'streak',
  SPECIAL = 'special'
}

export interface AchievementCondition {
  type: 'count' | 'score' | 'streak' | 'time' | 'special';
  target: number;
  metric: string;
}

export enum AchievementRarity {
  COMMON = 'common',
  UNCOMMON = 'uncommon',
  RARE = 'rare',
  EPIC = 'epic',
  LEGENDARY = 'legendary'
}

export interface MultiplayerSession {
  _id: string;
  quizId: string;
  hostId: string;
  participants: MultiplayerParticipant[];
  settings: MultiplayerSettings;
  status: SessionStatus;
  currentQuestion?: number;
  leaderboard: LeaderboardEntry[];
  createdAt: Date;
  startedAt?: Date;
  endedAt?: Date;
}

export interface MultiplayerParticipant {
  userId: string;
  username: string;
  avatar?: string;
  score: number;
  answers: Answer[];
  isReady: boolean;
  joinedAt: Date;
}

export interface MultiplayerSettings {
  maxParticipants: number;
  questionTimeLimit: number;
  showLeaderboard: boolean;
  allowLateJoin: boolean;
  powerUpsEnabled: boolean;
}

export enum SessionStatus {
  WAITING = 'waiting',
  STARTING = 'starting',
  IN_PROGRESS = 'in_progress',
  FINISHED = 'finished',
  CANCELLED = 'cancelled'
}

export interface LeaderboardEntry {
  userId: string;
  username: string;
  avatar?: string;
  score: number;
  rank: number;
  correctAnswers: number;
  averageTime: number;
}

export interface LearningPath {
  _id: string;
  userId: string;
  subject: string;
  level: DifficultyLevel;
  quizzes: LearningPathQuiz[];
  progress: number; // percentage
  estimatedTimeToComplete: number; // in hours
  createdAt: Date;
  updatedAt: Date;
}

export interface LearningPathQuiz {
  quizId: string;
  order: number;
  isCompleted: boolean;
  bestScore?: number;
  attempts: number;
  unlocked: boolean;
  prerequisites: string[]; // Quiz IDs
}

export interface Analytics {
  _id: string;
  userId: string;
  type: AnalyticsType;
  data: Record<string, any>;
  timestamp: Date;
}

export enum AnalyticsType {
  QUIZ_ATTEMPT = 'quiz_attempt',
  QUESTION_ANSWER = 'question_answer',
  TIME_SPENT = 'time_spent',
  DIFFICULTY_ADJUSTMENT = 'difficulty_adjustment',
  LEARNING_PROGRESS = 'learning_progress',
  USER_INTERACTION = 'user_interaction'
}

// API Response Types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
  pagination?: PaginationInfo;
}

export interface PaginationInfo {
  page: number;
  limit: number;
  total: number;
  pages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

// Authentication Types
export interface LoginRequest {
  email: string;
  password: string;
  rememberMe?: boolean;
}

export interface RegisterRequest {
  username: string;
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  role?: UserRole;
}

export interface AuthResponse {
  user: User;
  accessToken: string;
  refreshToken: string;
  expiresIn: number;
}

// Socket Events
export interface SocketEvents {
  // Connection
  connect: () => void;
  disconnect: () => void;
  
  // Multiplayer
  joinRoom: (sessionId: string) => void;
  leaveRoom: (sessionId: string) => void;
  playerJoined: (participant: MultiplayerParticipant) => void;
  playerLeft: (userId: string) => void;
  gameStarted: () => void;
  questionChanged: (questionIndex: number) => void;
  answerSubmitted: (answer: Answer) => void;
  leaderboardUpdated: (leaderboard: LeaderboardEntry[]) => void;
  gameEnded: (results: LeaderboardEntry[]) => void;
  
  // Notifications
  notification: (notification: Notification) => void;
  achievementUnlocked: (achievement: Achievement) => void;
}

export interface Notification {
  _id: string;
  userId: string;
  type: NotificationType;
  title: string;
  message: string;
  data?: Record<string, any>;
  isRead: boolean;
  createdAt: Date;
}

export enum NotificationType {
  QUIZ_INVITATION = 'quiz_invitation',
  ACHIEVEMENT = 'achievement',
  FRIEND_REQUEST = 'friend_request',
  STUDY_GROUP = 'study_group',
  SYSTEM = 'system'
}
